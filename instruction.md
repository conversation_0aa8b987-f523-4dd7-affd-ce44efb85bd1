# Capstone Project Instructions

## Overview

You are a web development and AI expert assistant helping with a capstone project focused on an **AI-Powered Learning Management System (LMS)**.

As you near the completion of the programme, dive into an engaging capstone project on an **AI-Powered Learning Management System (LMS)**. The project is divided into four main sections that test your knowledge—from frontend to backend to understanding AI features.

> **Note:**
> This capstone is **not** intended to deliver a fully integrated, production-ready website or unified UI. Its primary aim is to assess your understanding of the topics covered in the programme. While real-world applications demand security best practices, **this capstone is a prototype**, so comprehensive security and compliance are not enforced or assessed.

## Learning Outcomes

- Build responsive UIs using **HTML**, **CSS**, and **Bootstrap**.
- Add interactivity with **JavaScript** and manage UI state with **React**.
- Develop **RESTful APIs** using **Express.js**.
- Handle **data validation** and **API integration** with the frontend.
- Design, manage, and query **relational** and **non-relational databases** using **MySQL** and **MongoDB**.
- Understand the **concept and architecture of Smart Search** in a full-stack AI-powered LMS.

## Section-wise Submission Instructions

### Section 1: Frontend

**HTML, CSS, Bootstrap**

- Read `index.html`
- Create new file from index.html: `Capstone_Section1_HTML_Heimer.html`

**JavaScript**

- Read `javascript.html`
- Create new file from javascript.html: `Capstone_Section1_JS_Heimer.html`

**React**

- Follow requirements in `react/requirement.md`

### Section 2: Backend - Express.js (Part 1)

- Follow requirements in `express/requirement.md`

### Section 3: Backend - Databases

- Create a Express project folder

**MySQL Queries**

- Create new file containing all MySQL queries: `Capstone_Section3_SQL_Heimer.sql`

**MongoDB**

- Follow requirements in `database/mongo/requirement.md`

### Section 4: AI-Powered Features

- Create new file: `Capstone_Section4_Heimer.md`

## Configuration

**Input Folder**: `{INPUT_FOLDER}` = `ai-features`

## Step-by-Step Process

### 1. Initial Setup and Understanding

- **First**, read `rubric.md` to understand the grading criteria and expectations
- **Then**, examine the `{INPUT_FOLDER}/` folder structure and requirements

### 2. Task Focus

- **ONLY** complete tasks described in the `{INPUT_FOLDER}/` folder
- **IGNORE** other folders or files not related to the current task
- Follow the specific steps outlined in `{INPUT_FOLDER}/requirement.md`

### 3. Requirements Analysis

The current section focuses on tasks specific to the `{INPUT_FOLDER}` folder:

- Read and understand the requirements in `{INPUT_FOLDER}/requirement.md`
- Complete all tasks as specified in the requirements document
- Follow any specific formatting, word count, or technical requirements
- Apply relevant best practices for the technology or concept being covered

### 4. Deliverable

- Create and update your responses according to the deliverable specifications in `{INPUT_FOLDER}/requirement.md`
- **Important**: Files created should be placed in the `{INPUT_FOLDER}/` directory and exactly match the requirements specified in that folder
- For AI-Powered Features section: Create `{INPUT_FOLDER}/Capstone_Section4_Heimer.md`
- Follow the naming convention from Section-wise Submission Instructions above
- Ensure file formats, content structure, and output specifications align with the requirements document
- Ensure all tasks are completed thoroughly and meet the specified criteria

### 5. Quality Assurance

- Follow relevant best practices for the technology or concepts being covered
- Meet all grading rubric requirements
- Double-check your work against both the rubric and task instructions
- Ensure accuracy, completeness, and clarity in all responses
- Test functionality if code implementation is required

### 6. Final Steps

- Update the final result to `{INPUT_FOLDER}/Capstone_Section#_Heimer.md` upon completion
- Verify all deliverables are properly formatted and complete
- Ensure all files are saved in the correct locations as specified in the Section-wise Submission Instructions

## Submission Checklist

Your submission will be considered **complete** if it:

- Includes **all critical elements** outlined in the Section-wise Submission Instructions above
- Is graded based on the **rubric.md** available in the project root
- **Adheres to submission guidelines** and file placement requirements
- Files are created in the correct `{INPUT_FOLDER}/` directory structure

> 📄 Reference: **rubric.md** for detailed grading criteria

**Note:** This is a **required activity** and contributes to your **programme completion.**
