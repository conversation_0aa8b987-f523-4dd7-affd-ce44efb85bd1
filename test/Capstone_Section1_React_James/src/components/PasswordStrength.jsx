import React, { useEffect, useState } from "react";

export default function PasswordStrength({ password }) {
  const [passwordStrength, setPasswordStrength] = useState("");

  useEffect(() => {
    if (password.length >= 6 && /\d/.test(password)) {
      setPasswordStrength("Strong password");
    } else if (password.length >= 1) {
      setPasswordStrength("Weak password");
    } else {
      setPasswordStrength("");
    }
  }, [password]);

  return (
    <div>
      <p>{passwordStrength}</p>
    </div>
  );
}
