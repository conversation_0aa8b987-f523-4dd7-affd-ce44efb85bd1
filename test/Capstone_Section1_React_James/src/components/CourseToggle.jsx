import React, { useState } from "react";

export default function CourseToggle({ password }) {
  const [isVisible, setVisible] = useState(false);

  return (
    <div>
      <button type="button" onClick={() => setVisible(!isVisible)}>
        Show Description
      </button>
      {isVisible && (
        <p>
          This course covers React fundamentals including components, JSX, and
          props.
        </p>
      )}
    </div>
  );
}
