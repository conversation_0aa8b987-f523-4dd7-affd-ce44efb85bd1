USE lms_db;
-- 1. Create instructors table
CREATE TABLE instructors (
instructor_id INT AUTO_INCREMENT PRIMARY KEY,
name VARCHAR(100),
email VARCHAR(100) UNIQUE
);
-- Insert the instructors
INSERT INTO instructors (name, email)
VALUES
('<PERSON><PERSON>', '<EMAIL>'),
('<PERSON>', '<EMAIL>');
-- 2. Add new user <PERSON>
INSERT INTO users (name, email, password)
VALUES ('<PERSON>', '<EMAIL>', 'daniel123');
-- Enroll <PERSON> in CSS Design
INSERT INTO enrollments (user_id, course_id, enrollment_date)
VALUES (4, 2, '2025-07-06');
-- Query all users enrolled in CSS Design
SELECT u.name, u.email
FROM users u
JOIN enrollments e ON u.user_id = e.user_id
JOIN courses c ON e.course_id = c.course_id
WHERE c.course_name = 'CSS Design';
