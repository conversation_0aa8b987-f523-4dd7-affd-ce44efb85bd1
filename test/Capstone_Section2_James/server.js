// Import the express module
const express = require("express");
const cors = require("cors");
// Create an Express application
const app = express();

// Define the port number to run the server on
const PORT = 3000;

// 1. Middleware to parse JSON bodies in requests
app.use(cors());
app.use(express.json());

// 2. Basic GET route for homepage
app.get("/", (req, res) => {
  res.send("Welcome to the LMS backend!");
});
// 3. GET route to return a sample list of courses
app.get("/courses", (req, res) => {
  const courses = [
    { id: 1, name: "React for Beginners" },
    { id: 2, name: "Intro to Data Science" },
    { id: 3, name: "AI Fundamentals" },
  ];
  res.json(courses); // Send the array as JSON
});

app.post("/enroll", (req, res) => {
  const { userId, courseId } = req.body;

  if (!userId || !courseId) {
    return res
      .status(400)
      .json({ message: "Missing userId or courseId in request." });
  }

  res.status(200).json({
    message: `User ${userId} successfully enrolled in course ${courseId}.`,
  });
});

// 4. Start the Express server and listen on PORT
app.listen(PORT, () => {
  console.log(`Server running on ${PORT}`);
});
