import { render, screen } from "@testing-library/react";
import App from "./App";

test("renders LMS components", () => {
  render(<App />);
  const loginElement = screen.getByText(/Login to LMS/i);
  const passwordElement = screen.getByText(/Password Strength Checker/i);
  const courseElement = screen.getByText(/Course Information/i);
  expect(loginElement).toBeInTheDocument();
  expect(passwordElement).toBeInTheDocument();
  expect(courseElement).toBeInTheDocument();
});
