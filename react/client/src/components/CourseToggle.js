import React, { useState, useRef, useEffect } from "react";

function CourseToggle() {
  const [isVisible, setIsVisible] = useState(false);
  const descriptionRef = useRef(null);

  const toggleDescription = () => {
    setIsVisible(!isVisible);
  };

  useEffect(() => {
    if (
      isVisible &&
      descriptionRef.current &&
      descriptionRef.current.scrollIntoView
    ) {
      // Scroll to the description with smooth behavior
      descriptionRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, [isVisible]);

  return (
    <div style={{ padding: "20px" }}>
      <h2>Course Information</h2>
      <button
        onClick={toggleDescription}
        style={{ padding: "8px 16px", marginBottom: "10px" }}
      >
        {isVisible ? "Hide Description" : "Show Description"}
      </button>
      {isVisible && (
        <p
          ref={descriptionRef}
          style={{
            marginTop: "10px",
            padding: "10px",
            backgroundColor: "#f0f0f0",
            borderRadius: "4px",
          }}
        >
          This course covers React fundamentals including components, JSX, and
          props.
        </p>
      )}
    </div>
  );
}

export default CourseToggle;
