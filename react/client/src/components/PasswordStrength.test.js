import { render, screen, fireEvent } from '@testing-library/react';
import PasswordStrength from './PasswordStrength';

test('renders password strength checker', () => {
  render(<PasswordStrength />);
  const heading = screen.getByText(/Password Strength Checker/i);
  const input = screen.getByPlaceholderText(/Enter your password/i);
  const button = screen.getByText(/Check Strength/i);
  
  expect(heading).toBeInTheDocument();
  expect(input).toBeInTheDocument();
  expect(button).toBeInTheDocument();
});

test('shows weak password for short password', () => {
  render(<PasswordStrength />);
  const input = screen.getByPlaceholderText(/Enter your password/i);
  const button = screen.getByText(/Check Strength/i);
  
  fireEvent.change(input, { target: { value: '123' } });
  fireEvent.click(button);
  
  const message = screen.getByText(/Weak password/i);
  expect(message).toBeInTheDocument();
});

test('shows strong password for long password with number', () => {
  render(<PasswordStrength />);
  const input = screen.getByPlaceholderText(/Enter your password/i);
  const button = screen.getByText(/Check Strength/i);
  
  fireEvent.change(input, { target: { value: 'password123' } });
  fireEvent.click(button);
  
  const message = screen.getByText(/Strong password/i);
  expect(message).toBeInTheDocument();
});

test('shows weak password for long password without number', () => {
  render(<PasswordStrength />);
  const input = screen.getByPlaceholderText(/Enter your password/i);
  const button = screen.getByText(/Check Strength/i);
  
  fireEvent.change(input, { target: { value: 'password' } });
  fireEvent.click(button);
  
  const message = screen.getByText(/Weak password/i);
  expect(message).toBeInTheDocument();
});
