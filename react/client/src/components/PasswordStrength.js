import React, { useState } from "react";

function PasswordStrength() {
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");

  const checkStrength = () => {
    if (password.length < 6) {
      setMessage("Weak password");
    } else if (/\d/.test(password)) {
      setMessage("Strong password");
    } else {
      setMessage("Weak password");
    }
  };

  return (
    <div style={{ padding: "20px" }}>
      <h2>Password Strength Checker</h2>
      <input
        type="password"
        placeholder="Enter your password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        style={{ marginBottom: "10px", display: "block", padding: "8px", width: "200px" }}
      />
      <button 
        onClick={checkStrength}
        style={{ padding: "8px 16px", marginBottom: "10px" }}
      >
        Check Strength
      </button>
      {message && <p style={{ marginTop: "10px", fontWeight: "bold" }}>{message}</p>}
    </div>
  );
}

export default PasswordStrength;
