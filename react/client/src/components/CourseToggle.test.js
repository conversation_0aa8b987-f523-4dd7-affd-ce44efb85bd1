import { render, screen, fireEvent } from "@testing-library/react";
import CourseToggle from "./CourseToggle";

// Mock scrollIntoView for testing
beforeAll(() => {
  Element.prototype.scrollIntoView = jest.fn();
});

test("renders course toggle component", () => {
  render(<CourseToggle />);
  const heading = screen.getByText(/Course Information/i);
  const button = screen.getByText(/Show Description/i);

  expect(heading).toBeInTheDocument();
  expect(button).toBeInTheDocument();
});

test("toggles course description visibility", () => {
  render(<CourseToggle />);
  const button = screen.getByText(/Show Description/i);

  // Initially description should not be visible
  expect(
    screen.queryByText(/This course covers React fundamentals/i)
  ).not.toBeInTheDocument();

  // Click to show description
  fireEvent.click(button);
  expect(
    screen.getByText(/This course covers React fundamentals/i)
  ).toBeInTheDocument();
  expect(screen.getByText(/Hide Description/i)).toBeInTheDocument();

  // Click to hide description
  fireEvent.click(button);
  expect(
    screen.queryByText(/This course covers React fundamentals/i)
  ).not.toBeInTheDocument();
  expect(screen.getByText(/Show Description/i)).toBeInTheDocument();
});
