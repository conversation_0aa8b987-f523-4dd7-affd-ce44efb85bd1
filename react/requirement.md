# REACT.JS

## 🎯 Objective

This code helps you learn key **React.js** skills:

- Creating and using **components**
- Writing **JSX**
- Managing data with `useState`
- Passing data using **props**
- Navigating pages in a **single-page application (SPA)**

You’ll practise building reusable components, handling user input, and creating fast, responsive apps.

---

## 📁 Project Setup

Read a main project folder named:

```

client

```

### 🔍 Understand Code Structure

| File/Folder   | Purpose                                                    |
| ------------- | ---------------------------------------------------------- |
| `App.js`      | Defines routes for the LMS (home, login, courses, chatbot) |
| `pages/`      | Contains different pages                                   |
| `components/` | Reusable UI components (e.g., `NavBar`, `CourseCard`)      |
| `assets/`     | Holds images, icons                                        |
| `public/`     | Static HTML assets                                         |
| `index.js`    | React app entry point                                      |

---

## 🧩 Tasks

### 1. ✅ Password Strength Checker

Create a component named:

```

components/PasswordStrength.js

```

#### 📌 Features:

- Input field for password
- Button: **"Check Strength"**
- Show result below the input:
  - If password `< 6` characters → **Weak password**
  - If password `≥ 6` characters **and** contains number → **Strong password**

#### 💡 Hints:

- Use `useState()` to manage `password` and `message`
- Use `.length` to check string length
- Use RegEx: `/\d/` to detect numbers
- Example logic:
  ```js
  if (password.length < 6) {
    setMessage("Weak password");
  } else if (/\d/.test(password)) {
    setMessage("Strong password");
  }
  ```

### 2. ✅ Toggle Course Description

Create a component named:

```
components/CourseToggle.js
```

#### 📌 Features:

- Button: **"Show Description"**
- On click, toggle visibility of this text:

  ```
  This course covers React fundamentals including components, JSX, and props.
  ```

- Button toggles text on/off

#### 💡 Hints:

- Use `useState()` for a boolean like `isVisible`
- Button toggles state:

  ```js
  setIsVisible(!isVisible);
  ```

- Use conditional rendering:

  ```jsx
  {
    isVisible && <p>...</p>;
  }
  ```
