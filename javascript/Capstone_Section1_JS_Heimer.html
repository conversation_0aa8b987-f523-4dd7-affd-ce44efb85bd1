<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>AI LMS - Module 5: JavaScript Interactivity</title>
    <style>
      body {
        font-family: "Segoe UI", sans-serif;
        padding: 20px;
        background-color: #f5f7fa;
      }
      .section {
        background: white;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
      h2 {
        color: #1a73e8;
      }
      .highlight {
        color: green;
        font-weight: bold;
      }
      input,
      button {
        padding: 8px;
        margin: 5px 0;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <h1>AI-Powered LMS: JavaScript Practice</h1>

    <!-- Example 1: Button Interactivity with Alert -->
    <div class="section">
      <h2>1. Enroll in Course</h2>
      <button onclick="enroll()">Click to Enroll</button>
      <script>
        function enroll() {
          alert(
            " You have been enrolled in the 'JavaScript Essentials' course!"
          );
        }
      </script>
    </div>

    <!-- Example 2: DOM Manipulation -->
    <div class="section">
      <h2>2. AI Suggestion Box</h2>
      <p id="suggestionText">
        Click the button below to get a course suggestion.
      </p>
      <button onclick="getSuggestion()">Get AI Suggestion</button>

      <script>
        function getSuggestion() {
          let text = document.getElementById("suggestionText");
          text.textContent = " We recommend: 'Responsive Web Design' next!";
          text.style.color = "#1a73e8";
        }
      </script>
    </div>

    <!-- Task 1: Validate Learner Email -->
    <div class="section">
      <h2>3. Validate Learner Email</h2>
      <form onsubmit="return validateEmail()">
        <input
          type="text"
          id="email"
          placeholder="Enter your email to receive updates"
        />
        <button type="submit">Submit</button>
      </form>
      <p id="emailMessage" class="highlight"></p>

      <script>
        // TASK: Complete this function
        function validateEmail() {
          let emailInput = document.getElementById("email").value;
          let messageElement = document.getElementById("emailMessage");

          // Check if email includes '@' symbol
          if (!emailInput.includes("@")) {
            // If email does not include '@', show "Invalid email address" in red and return false
            messageElement.textContent = "Invalid email address";
            messageElement.style.color = "red";
            return false;
          } else {
            // If valid, show "Email accepted!" in green and prevent form submission
            messageElement.textContent = "Email accepted!";
            messageElement.style.color = "green";
            return false; // Prevent form submission so message stays visible
          }
        }
      </script>
    </div>
    <!-- Task 2: Keypress Event Handling -->
    <div class="section">
      <h2>4. Type Your Learning Goal</h2>
      <input
        type="text"
        id="goalInput"
        placeholder="e.g., Master JavaScript..."
      />
      <p id="goalOutput">Your goal:</p>

      <script>
        // TASK: Add keypress event on #goalInput
        // Each time a key is pressed, update #goalOutput to include the latest text

        // Select the input element
        let goalInput = document.getElementById("goalInput");

        // Add event listener for 'input' event (better than keypress as it captures all input changes)
        goalInput.addEventListener("input", function () {
          // Get the current value from the input
          let inputValue = goalInput.value;

          // Update the output paragraph with the current input value
          document.getElementById("goalOutput").textContent =
            "Your goal: " + inputValue;
        });
      </script>
    </div>
  </body>
</html>
