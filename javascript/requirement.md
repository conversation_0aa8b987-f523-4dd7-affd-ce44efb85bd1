# JAVASCRIPT

## Objective

This code helps you learn basic **JavaScript** skills such as:

- Making buttons work
- Changing page content
- Checking form inputs
- Handling user actions

You’ll practice writing functions and making the page respond to user interaction—important steps in building interactive websites.

---

## Understand the Code Components

The project covers:

- **Basic interactivity** (e.g., button alerts)
- **DOM manipulation** (changing text and styles dynamically)
- **Form validation** using JavaScript
- **Event handling** for user inputs (keypress/input events)
- Internal CSS styles included within the `<style>` tag for layout and presentation

---

## Tasks

### 1. Complete the Email Validation Function

In the section titled **“Validate Learner Email”**, complete the JavaScript function `validateEmail()` so it checks whether the user's input includes an `@` symbol.

#### Requirements:

- If the email is invalid (no `@`), display:  
  `Invalid email address`
- If the email is valid, display:  
  `Email accepted!`

#### Hints:

- Use:
  ```javascript
  document.getElementById("email").value;
  ```

* Use:

  ```javascript
  includes("@");
  ```

* Update message with:

  ```javascript
  document.getElementById("emailMessage").textContent = "...";
  ```

* Use `return false` to prevent form submission if invalid

---

### 2. Add Keypress (Input) Event for Goal Typing

In the section **“Type Your Learning Goal”**, make the text below the input box dynamically update as the learner types their goal.

#### Requirements:

- As the learner types in the input box, the `<p>` below should update with the full input value

#### Hints:

- Select the input element:

  ```javascript
  let goalInput = document.getElementById("goalInput");
  ```

- Use event listener:

  ```javascript
  goalInput.addEventListener('input', function() {...})
  ```

- Get input value with:

  ```javascript
  goalInput.value;
  ```

- Update output:

  ```javascript
  document.getElementById("goalOutput").textContent = "Your goal: " + ...
  ```
