## Capstone Project Grading Rubric

**Assignment submitter:** Heimer
**Grader:** AI Assistant

---

| No  | Criteria                                                             | Proficient (5 to >3.0 pts)                                                        | Developing (3 to >0.0 pts)                                           | Below Expectation (0 pts)             | Points | Result |
| --- | -------------------------------------------------------------------- | --------------------------------------------------------------------------------- | -------------------------------------------------------------------- | ------------------------------------- | ------ | ------ |
| 1   | **Add 2 Flexbox Feature Boxes**                                      | Both boxes present, correct titles & structured                                   | One box added or incorrect HTML structure                            | Incorrect submission OR No Submission | 5 pts  |        |
| 2   | **Add 2 Bootstrap Cards**                                            | Uses Bootstrap grid, each card includes title, text & button                      | Layout/semantics are incorrect or one card is missing                | Incorrect submission OR No Submission | 5 pts  |        |
| 3   | **JS Email Validation**                                              | Fully functional validation: checks “@”, updates DOM, handles submit              | Logic errors, missing message update or submit handling              | Incorrect submission OR No Submission | 5 pts  |        |
| 4   | **JS Input Event Handling**                                          | Updates dynamically as user types using event listener                            | Text updates incorrectly or on wrong event                           | Incorrect submission OR No Submission | 5 pts  |        |
| 5   | **Password Strength Checker (React)**                                | Checks length & number, shows message accordingly                                 | No regex, improper condition, or no message display                  | Incorrect submission OR No Submission | 5 pts  |        |
| 6   | **Course Description Toggle (React)**                                | Toggles description with button label updates                                     | Partially working or no conditional rendering                        | Incorrect submission OR No Submission | 5 pts  |        |
| 7   | **POST /enroll API**                                                 | Accepts JSON, returns correct response                                            | Response or request handling is incorrect                            | Incorrect submission OR No Submission | 5 pts  |        |
| 8   | **Error Handling for Missing Fields**                                | Returns 400 error and proper message when userId or courseId is missing           | Only partial validation or incorrect status/message                  | Incorrect submission or no submission | 5 pts  |        |
| 9   | **Create Instructors Table & Insert Records**                        | Correct SQL syntax: AUTO_INCREMENT, UNIQUE, and 3 valid inserts                   | Missing constraints or partial insertions                            | Incorrect submission or no submission | 5 pts  |        |
| 10  | **Add User + Enroll + JOIN Query**                                   | Executes all 3 SQL steps correctly and shows enrolled users                       | Only some parts completed or incorrect JOIN query                    | Incorrect submission or no submission | 5 pts  |        |
| 11  | **Create a New Entry in MongoDB Database**                           | Data is correctly inserted using appropriate commands                             | Data inserted is incomplete                                          | Incorrect submission or no submission | 5 pts  |        |
| 12  | **Explain How Smart Search Enhances UX**                             | Clear comparison with practical insights and relevant LMS examples                | Basic or unclear explanation with limited comparison                 | Incorrect submission or no submission | 5 pts  |        |
| 13  | **Describe Role of Frontend, Backend, and Database**                 | Clear explanation of each layer's role and their interactions in a full-stack LMS | Some roles unclear, missing, or lacks clarity on interactions        | Incorrect submission or no submission | 5 pts  |        |
| 14  | **Identify potential challenges and conceptually discuss solutions** | Well-reasoned challenges with thoughtful suggestions or strategies                | Vague or generic mention of challenges; solutions not well developed | Incorrect submission OR No Submission | 5 pts  |        |

---

**Total:** /70 pts
