Next, the backend is added using Express.js and Node.js. Students create REST APIs to manage data, logins, and app rules. The frontend from Section 1 uses these APIs to register users, log in, show courses, and track progress. This makes the website active and responsive to users. The backend also keeps the data safe and connects the user interface to the database.

Express.js (Node.js)
Express.js is a simple and fast tool that helps you build backend APIs using Node.js. Node.js lets you run JavaScript on the server (not just in the browser) and can handle things like web requests, files, and networks. But using only Node.js can be tricky and time-consuming. Express makes it easier by giving a clear and easy way to build web servers and APIs. It works on top of Node.js and helps you build backend features quickly and in an organised way.

Manage Courses and Learners
GET /courses → Fetch all available courses
POST /enroll → Enroll a user in a course
GET /user/:id/courses → View user progress
Serve AI-Based Recommendations
Endpoint like GET /recommend?interest=ai → return smart suggestions
In future, integrate Python ML model using child_process or via Flask microservice
Track Performance
Store progress like quiz scores, time spent, completed modules, etc.
Route example: POST /track-progress

Now, perform the below tasks:
Add a POST route /enroll to enroll a user in a course
Create a new POST endpoint /enroll that accepts a JSON body with userId and courseId. It should respond with a confirmation message like:
User 123 successfully enrolled in course 1.

Hints:

Use app.post('/enroll', (req, res) => { ... })
Access data via req.body.userId and req.body.courseId
Use res.json() to send a JSON response
Don’t forget to use express.json() middleware (already included above)
The output should resemble this image.
Add error handling for missing fields in the /enroll POST request
Improve the /enroll route so that if userId or courseId is missing from the request body, it responds with status code 400 and message:
Missing userId or courseId in request.

Hints:

Check if req.body.userId and req.body.courseId exist
Use res.status(400).json({ error: '...' }) to send error response
Otherwise, proceed with the enrollment confirmation
