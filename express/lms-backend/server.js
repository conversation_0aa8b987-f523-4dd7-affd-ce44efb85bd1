// Import the express module
const express = require("express");

// Create an Express application
const app = express();

// Define the port number to run the server on
const PORT = 3001;

// 1. Middleware to parse JSON bodies in requests
app.use(express.json());

// 2. Basic GET route for homepage
app.get("/", (req, res) => {
  res.send("Welcome to the LMS backend!");
});
// 3. GET route to return a sample list of courses
app.get("/courses", (req, res) => {
  const courses = [
    { id: 1, name: "React for Beginners" },
    { id: 2, name: "Intro to Data Science" },
    { id: 3, name: "AI Fundamentals" },
  ];
  res.json(courses); // Send the array as JSON
});

// 4. POST route to enroll a user in a course
app.post("/enroll", (req, res) => {
  // Check if userId and courseId are provided in the request body
  if (!req.body.userId || !req.body.courseId) {
    return res
      .status(400)
      .json({ error: "Missing userId or courseId in request." });
  }

  // Extract userId and courseId from request body
  const { userId, courseId } = req.body;

  // Send confirmation response
  res.json({
    message: `User ${userId} successfully enrolled in course ${courseId}.`,
  });
});

// 5. Start the Express server and listen on PORT
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
