# Capstone Rubric

This rubric outlines the evaluation criteria for the Capstone project. The total score is **70 points**.

---

## 🧱 Frontend Section (HTML, CSS, JavaScript, React)

### 1. Add 2 Flexbox Feature Boxes — **5 pts**

- **Proficient (5–3.1 pts):** Both boxes present, correct titles, and structured.
- **Developing (3–0.1 pts):** One box added or incorrect HTML structure.
- **Below Expectation (0 pts):** Incorrect submission OR no submission.

### 2. Add 2 Bootstrap Cards — **5 pts**

- **Proficient:** Uses Bootstrap grid; each card includes title, text, and button.
- **Developing:** Layout/semantics incorrect or one card is missing.
- **Below Expectation:** Incorrect submission OR no submission.

### 3. JavaScript Email Validation — **5 pts**

- **Proficient:** Fully functional validation — checks “@”, updates DOM, handles submit.
- **Developing:** Logic errors, missing message update, or submit handling.
- **Below Expectation:** Incorrect submission OR no submission.

### 4. JavaScript Input Event Handling — **5 pts**

- **Proficient:** Updates dynamically as user types using event listener.
- **Developing:** Text updates incorrectly or on wrong event.
- **Below Expectation:** Incorrect submission OR no submission.

### 5. Password Strength Checker (React) — **5 pts**

- **Proficient:** Checks length & number; shows message accordingly.
- **Developing:** No regex, improper condition, or no message display.
- **Below Expectation:** Incorrect submission OR no submission.

### 6. Course Description Toggle (React) — **5 pts**

- **Proficient:** Toggles description with button label updates.
- **Developing:** Partially working or no conditional rendering.
- **Below Expectation:** Incorrect submission OR no submission.

---

## 🖥 Backend & API Section (Express)

### 7. POST `/enroll` API — **5 pts**

- **Proficient:** Accepts JSON, returns correct response.
- **Developing:** Response or request handling is incorrect.
- **Below Expectation:** Incorrect submission OR no submission.

### 8. Error Handling for Missing Fields — **5 pts**

- **Proficient:** Returns 400 error & message when `userId` or `courseId` is missing.
- **Developing:** Only partial check or incorrect status/message.
- **Below Expectation:** Incorrect submission OR no submission.

---

## 🗃 Database Section (MySQL & MongoDB)

### 9. Create Instructors Table & Insert Records — **5 pts**

- **Proficient:** Correct SQL syntax — `AUTO_INCREMENT`, `UNIQUE`, 3 valid inserts.
- **Developing:** Missing constraints or partial inserts.
- **Below Expectation:** Incorrect submission OR no submission.

### 10. Add User + Enroll + JOIN Query — **5 pts**

- **Proficient:** Executes all 3 SQL steps correctly, shows enrolled user.
- **Developing:** Only some parts completed or JOIN query incorrect.
- **Below Expectation:** Incorrect submission OR no submission.

### 11. Create Entry in MongoDB Database — **5 pts**

- **Proficient:** Data inserted correctly using the appropriate commands.
- **Developing:** Data added is incomplete.
- **Below Expectation:** Incorrect submission OR no submission.

---

## 🤖 AI-Powered Smart Search Section

### 12. Explain How Smart Search Enhances UX vs Standard Search — **5 pts**

- **Proficient:** Clear comparison with practical insights and LMS-related examples.
- **Developing:** Basic explanation with limited comparison or unclear LMS context.
- **Below Expectation:** Incorrect submission OR no submission.

### 13. Describe Frontend, Backend, and DB Roles in Smart Search — **5 pts**

- **Proficient:** Clear explanation of each layer’s role and how they interact.
- **Developing:** Some roles unclear or missing; lacks clarity on interaction.
- **Below Expectation:** Incorrect submission OR no submission.

### 14. Identify Challenges & Discuss Solutions — **5 pts**

- **Proficient:** Well-reasoned challenges with thoughtful suggestions or strategies.
- **Developing:** Vague or generic challenges; solutions not well developed.
- **Below Expectation:** Incorrect submission OR no submission.

---

**✅ Total Points: 70**

> Ensure your submission meets all requirements to achieve full credit.  
> Use this rubric to self-check your work before submitting.
