# HTML, CSS, BOOTSTRAP

## Objective

This project helps you learn how to create flexible layouts using **Flexbox** and **Bootstrap**. It includes a menu bar and a section built with Flexbox. You’ll also add more Flexbox and Bootstrap cards. This will help you understand modern CSS layouts and how to build user interfaces using components.

---

## Instructions – HTML, CSS, Bootstrap

**Goal**: Create new and code `index.html` to match the appearance shown in `output.png`.

---

## Know What the Code Uses

The code includes:

- Semantic HTML tags like `<main>`, `<section>`, `<nav>`
- Internal CSS with **Flexbox** for layout
- **Bootstrap 5** for responsive design and pre-built components

---

## Step 3: Run the Page

Now, perform the tasks below:

### 1. Add Two More Feature Boxes Using Flexbox

In the section where the **“Adaptive Courses”** card is, add **two more cards**:

- One titled **“Progress Tracking”**
- One titled **“Real-time Assessments”**

---

### 2. Add a New Section Below the Feature Boxes

Create **two Bootstrap cards** placed **side by side**:

- One card should say: **“HTML Module”**
- The other: **“CSS Module”**

---

## Hints

Start with a container structure using **Bootstrap Grid**:

```html
<div class="row">
  <div class="col-md-6"><!-- Card 1 --></div>
  <div class="col-md-6"><!-- Card 2 --></div>
</div>
```

Inside each column, add a Bootstrap card using:

- `class="card"`
- Inside the card, include:

  - `card-body`
  - `card-title`
  - `card-text`
  - `btn btn-primary`

Wrap this layout inside a `<section>` tag to keep your structure semantic.
