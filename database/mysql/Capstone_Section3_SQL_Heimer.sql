-- Capstone Section 3: MySQL Database Operations
-- Learning Management System (LMS) Database

-- Step 1: Create and Use Database
CREATE DATABASE lms_db;
USE lms_db;

-- Table 1: Users
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(100),
    email VARCHAR(100) UNIQUE,
    password VARCHAR(255)
);

-- Table 2: Courses
CREATE TABLE courses (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100),
    description TEXT
);

-- Table 3: Enrollments
CREATE TABLE enrollments (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    course_id INT,
    enrollment_date DATE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id),
    FOREIG<PERSON> KEY (course_id) REFERENCES courses(course_id)
);

-- Table 4: Assessments
CREATE TABLE assessments (
    assessment_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT,
    title VARCHAR(100),
    max_score INT,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (course_id) REFERENCES courses(course_id)
);

-- Step 2: Insert Sample Data

-- Insert Users
INSERT INTO users (name, email, password) VALUES
('Alice Johnson', '<EMAIL>', 'alice123'),
('Bob Smith', '<EMAIL>', 'bob123'),
('Charlie Lee', '<EMAIL>', 'charlie123');

-- Insert Courses
INSERT INTO courses (course_name, description) VALUES
('HTML Basics', 'Introduction to HTML and web structure.'),
('CSS Design', 'Learn how to style websites using CSS.'),
('MySQL for Beginners', 'Basic concepts of relational databases.');

-- Insert Enrollments
INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES
(1, 1, '2024-01-10'),
(1, 3, '2024-02-05'),
(2, 2, '2024-02-15'),
(3, 1, '2024-03-01');

-- Insert Assessments
INSERT INTO assessments (course_id, title, max_score) VALUES
(1, 'HTML Quiz 1', 100),
(2, 'CSS Midterm', 80),
(3, 'MySQL Final Test', 90);

-- Step 3: CRUD Examples

-- Read Data (SELECT)
SELECT * FROM users;

SELECT u.name, c.course_name 
FROM enrollments e 
JOIN users u ON e.user_id = u.user_id 
JOIN courses c ON e.course_id = c.course_id 
WHERE u.user_id = 1;

-- Update Data (UPDATE)
UPDATE users SET email = '<EMAIL>' WHERE user_id = 1;

-- Delete Data (DELETE)
DELETE FROM enrollments WHERE enrollment_id = 2;

-- ========================================
-- CAPSTONE TASKS - GRADED COMPONENTS
-- ========================================

-- TASK 9: Create Instructors Table & Insert Records (5 pts)
-- Requirements: Correct SQL syntax, AUTO_INCREMENT, UNIQUE, 3 valid inserts

CREATE TABLE instructors (
    instructor_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100) UNIQUE
);

-- Insert 3 instructor records as required
INSERT INTO instructors (name, email) VALUES
('Dr. Sarah Wilson', '<EMAIL>'),
('Prof. Michael Chen', '<EMAIL>'),
('Ms. Emily Rodriguez', '<EMAIL>');

-- TASK 10: Add User + Enroll + JOIN Query (5 pts)
-- Requirements: Execute all 3 SQL steps correctly, show enrolled user

-- Step 1: Add Daniel Rose to users table
INSERT INTO users (name, email, password) VALUES
('Daniel Rose', '<EMAIL>', 'daniel123');

-- Step 2: Enroll Daniel in "CSS Design" course with today's date
INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES
((SELECT user_id FROM users WHERE email = '<EMAIL>'), 
 (SELECT course_id FROM courses WHERE course_name = 'CSS Design'), 
 CURDATE());

-- Step 3: JOIN Query to display all users enrolled in "CSS Design"
SELECT u.user_id, u.name, u.email, e.enrollment_date
FROM users u
JOIN enrollments e ON u.user_id = e.user_id
JOIN courses c ON e.course_id = c.course_id
WHERE c.course_name = 'CSS Design';

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify instructors table creation and data
SELECT * FROM instructors;

-- Verify Daniel Rose was added
SELECT * FROM users WHERE email = '<EMAIL>';

-- Verify all enrollments in CSS Design course
SELECT u.name, u.email, e.enrollment_date, c.course_name
FROM users u
JOIN enrollments e ON u.user_id = e.user_id
JOIN courses c ON e.course_id = c.course_id
WHERE c.course_name = 'CSS Design'
ORDER BY e.enrollment_date;

-- Show complete database state
SELECT 'USERS TABLE' as table_name;
SELECT * FROM users;

SELECT 'COURSES TABLE' as table_name;
SELECT * FROM courses;

SELECT 'INSTRUCTORS TABLE' as table_name;
SELECT * FROM instructors;

SELECT 'ENROLLMENTS TABLE' as table_name;
SELECT * FROM enrollments;

SELECT 'ASSESSMENTS TABLE' as table_name;
SELECT * FROM assessments;
