Objective:

This section guides users through MySQL database operations using Command Prompt. It covers creating a database, creating tables, inserting sample data, and performing basic CRUD operations. Users will also complete tasks such as adding instructors and enrolling new users to reinforce hands-on SQL skills in a learning management system (LMS) context.

Step 1: Create and Use a Database
CREATE DATABASE lms_db;
USE lms_db;

Table 1: Users
CREATE TABLE users ( user_id INT AUTO_INCREMENT PRIMARY KEY, name VA<PERSON>HA<PERSON>(100), email VARCHAR(100) UNIQUE, password VARCHAR(255) );

Table 2: Courses
CREATE TABLE courses ( course_id INT AUTO_INCREMENT PRIMARY KEY, course_name VARCHAR(100), description TEXT );

Table 3: Enrollments
CREATE TABLE enrollments ( enrollment_id INT AUTO_INCREMENT PRIMARY KEY, user_id INT, course_id INT, enrollment_date DATE, FOREIGN KEY (user_id) REFERENCES users(user_id), FOREIGN KEY (course_id) REFERENCES courses(course_id) );

Table 4: Assessments
CREATE TABLE assessments ( assessment_id INT AUTO_INCREMENT PRIMARY KEY, course_id INT, title VARCHAR(100), max_score INT, FOREIGN KEY (course_id) REFERENCES courses(course_id) );

Step 2: Insert Sample Data
INSERT INTO users (name, email, password) VALUES ('Alice Johnson', '<EMAIL>', 'alice123'), ('Bob Smith', '<EMAIL>', 'bob123'), ('Charlie Lee', '<EMAIL>', 'charlie123');

Insert Courses
INSERT INTO courses (course_name, description) VALUES ('HTML Basics', 'Introduction to HTML and web structure.'), ('CSS Design', 'Learn how to style websites using CSS.'), ('MySQL for Beginners', 'Basic concepts of relational databases.');

Insert Enrollments
INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES (1, 1, '2024-01-10'), (1, 3, '2024-02-05'), (2, 2, '2024-02-15'), (3, 1, '2024-03-01');

Insert Assessments
INSERT INTO assessments (course_id, title, max_score) VALUES (1, 'HTML Quiz 1', 100), (2, 'CSS Midterm', 80), (3, 'MySQL Final Test', 90);

Step 3: CRUD Examples
Read Data (SELECT)
SELECT \* FROM users;
SELECT u.name, c.course_name FROM enrollments e JOIN users u ON e.user_id = u.user_id JOIN courses c ON e.course_id = c.course_id WHERE u.user_id = 1;

Update Data (UPDATE)
UPDATE users SET email = '<EMAIL>' WHERE user_id = 1;

Delete Data (DELETE)
DELETE FROM enrollments WHERE enrollment_id = 2;

Now, perform the below tasks:

Create a new table named instructors:
instructor_id (Primary Key, auto-increment)
name (varchar)
email (unique)
Hint:

Use CREATE TABLE with AUTO_INCREMENT and UNIQUE.
Use INSERT INTO to add entries.
Enroll new user Daniel Rose (<EMAIL>, password: daniel123) in “CSS Design”
Add this user to the users table
Enroll him in the “CSS Design” course with today’s date
Write a query to display all users enrolled in “CSS Design”
Hint:

Find the course_id for “CSS Design” from the courses table.
Use INSERT INTO enrollments with correct user_id and course_id
Use JOIN to link all users, enrollments and courses.
