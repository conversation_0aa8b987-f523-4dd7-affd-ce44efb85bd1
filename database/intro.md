This section aims to equip students with practical skills in working with two popular databases: MySQL and MongoDB. Using MySQL through the Command Prompt, students will learn to create databases, design relational tables, insert sample data, and perform CRUD operations in a Learning Management System (LMS) context. With MongoDB, students will use MongoDB Compass and Express.js to design schemas, insert and retrieve documents, and build RESTful APIs for a school management system. This hands-on approach builds foundational knowledge in both SQL-based and NoSQL database systems.
