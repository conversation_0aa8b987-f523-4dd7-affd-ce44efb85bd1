Mongo DB

Objective:

Understand how to use MongoDB as a NoSQL database to design and manage a backend for a School Information System. Learners will model real-world entities such as schools, courses, and enrollments using Mongoose schemas, establish relationships through ObjectId references, and build RESTful APIs with Express.js.

Paste School Data in MongoDB Compass – School Collection (schoolsystem.schools)

```
[
  {
    "_id": {
      "$oid": "665f1fa4a7d3f1a0aabc1001"
    },
    "name": "Greenwood High School",
    "address": "123 Maple Street, Springfield",
    "principal": "Mr. <PERSON>"
  },
  {
    "_id": {
      "$oid": "665f1fa4a7d3f1a0aabc1002"
    },
    "name": "Riverside Public School",
    "address": "456 Oak Avenue, Riverdale",
    "principal": "<PERSON>. <PERSON>"
  }
]
// In the similar way Add Course (courseModel.js) and Enrollment (enrollmentModel.js) Data’s in MongoDB Compass
```

Steps for connecting models and routes with Server.js (#Main Express server)

Now perform the below task:

Create a new entry in ‘school’ in MongoDB database and insert a document with the following parameter:
“\_id”
“name”
“address”
“principal”
Hint:

Use MongoDB Compass
Use “ADD DATA”
